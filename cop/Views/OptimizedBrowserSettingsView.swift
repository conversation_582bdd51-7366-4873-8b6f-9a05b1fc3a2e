//
//  OptimizedBrowserSettingsView.swift
//  cop
//
//  Created by Augment Agent on 2025/6/2.
//

import SwiftUI

// MARK: - 优化后的浏览器设置视图
// 使用统一的BrowserNavigationDestination枚举
struct OptimizedBrowserSettingsView: View {
    @ObservedObject var browserViewModel: NewWebBrowserViewModel
    @Environment(\.dismiss) private var dismiss

    // 接收父级的导航路径
    @Binding var navigationPath: NavigationPath
    
    var body: some View {
        List {
            // 浏览行为设置
            browsingBehaviorSection
            
            // 数据与历史管理
            dataHistorySection
            
            // 隐私与安全设置
            privacySecuritySection
        }
        .listStyle(InsetGroupedListStyle())
        .navigationTitle("浏览器设置")
        .navigationBarTitleDisplayMode(.large)
    }
    
    // MARK: - 浏览行为设置
    private var browsingBehaviorSection: some View {
        Section {
            BrowserSettingsRow(
                icon: "globe",
                iconColor: AppDesignSystem.Colors.primary,
                title: "用户代理",
                subtitle: userAgentDescription,
                controlType: .navigation
            ) {
                // 使用延迟更新避免多次更新警告
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.01) {
                    navigationPath.append(BrowserNavigationDestination.userAgent)
                }
            }

            BrowserSettingsRow(
                icon: "magnifyingglass",
                iconColor: AppDesignSystem.Colors.icon,
                title: "默认搜索引擎",
                subtitle: browserViewModel.searchEngine,
                controlType: .navigation
            ) {
                // 使用延迟更新避免多次更新警告
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.01) {
                    navigationPath.append(BrowserNavigationDestination.searchEngine)
                }
            }
        } header: {
            BrowserSectionHeader("浏览行为", subtitle: "控制网站如何识别您的设备")
        }
    }
    
    // MARK: - 数据与历史管理
    private var dataHistorySection: some View {
        Section {
            BrowserSettingsRow(
                icon: "clock",
                iconColor: AppDesignSystem.Colors.primary,
                title: "历史记录管理",
                subtitle: "查看、清理历史记录",
                controlType: .navigation
            ) {
                // 使用延迟更新避免多次更新警告
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.01) {
                    navigationPath.append(BrowserNavigationDestination.history)
                }
            }
            
            BrowserSettingsRow(
                icon: "book",
                iconColor: AppDesignSystem.Colors.primary,
                title: "书签管理",
                subtitle: "管理收藏的网页",
                controlType: .navigation
            ) {
                // 导航到书签管理页面
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.01) {
                    navigationPath.append(BrowserNavigationDestination.bookmarks)
                }
            }

            BrowserSettingsRow(
                icon: "database",
                iconColor: AppDesignSystem.Colors.primary,
                title: "数据管理",
                subtitle: "管理浏览器数据",
                controlType: .navigation
            ) {
                // 使用延迟更新避免多次更新警告
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.01) {
                    navigationPath.append(BrowserNavigationDestination.dataManagement)
                }
            }
        } header: {
            BrowserSectionHeader("数据与历史管理", subtitle: "管理您的浏览历史和数据")
        }
    }
    
    // MARK: - 隐私与安全设置
    private var privacySecuritySection: some View {
        Section {
            BrowserSettingsRow(
                icon: "shield.lefthalf.filled",
                iconColor: AppDesignSystem.Colors.primary,
                title: "安全与隐私",
                subtitle: "HTTPS强制、追踪保护、数据加密等",
                controlType: .navigation
            ) {
                // 使用延迟更新避免多次更新警告
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.01) {
                    navigationPath.append(BrowserNavigationDestination.security)
                }
            }
        } header: {
            BrowserSectionHeader("隐私与安全", subtitle: "保护您的在线安全和隐私")
        }
    }
    
    // MARK: - 计算属性
    private var userAgentDescription: String {
        switch browserViewModel.currentUserAgent {
        case .windowsPC:
            return "Windows PC"
        case .macPC:
            return "Mac PC"
        case .android:
            return "Android"
        case .ios:
            return "iOS"
        }
    }
}

// MARK: - 预览
struct OptimizedBrowserSettingsView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            OptimizedBrowserSettingsView(
                browserViewModel: NewWebBrowserViewModel(),
                navigationPath: .constant(NavigationPath())
            )
        }
    }
}
