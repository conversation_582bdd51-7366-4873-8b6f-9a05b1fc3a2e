//
//  copApp.swift
//  cop
//
//  Created by 阿亮 on 2025/5/31.
//

import SwiftUI

@main
struct copApp: App {
    @StateObject private var browserViewModel = NewWebBrowserViewModel()
    
    // 初始化系统错误处理器
    init() {
        // 启动系统错误处理器
        _ = SystemErrorHandler.shared
        
        // 配置日志级别
        #if DEBUG
        print("🚀 [COP浏览器] 启动 - 开发模式，错误处理器已激活")
        #else
        print("🚀 [COP浏览器] 启动 - 发布模式")
        #endif
    }

    var body: some Scene {
        WindowGroup {
            ContentView()
                .environmentObject(browserViewModel)
                .onAppear {
                    // 应用启动时的初始化
                    Task { @MainActor in
                        await initializeApp()
                    }
                }
                .onReceive(NotificationCenter.default.publisher(for: UIApplication.willTerminateNotification)) { _ in
                    // 应用即将终止时保存所有数据并生成诊断报告
                    Task { @MainActor in
                        await handleAppTermination()
                    }
                }
                .onReceive(NotificationCenter.default.publisher(for: UIApplication.didEnterBackgroundNotification)) { _ in
                    // 应用进入后台时保存所有数据
                    Task { @MainActor in
                        await handleAppBackground()
                    }
                }
        }
    }
    
    // MARK: - 应用初始化
    @MainActor
    private func initializeApp() async {
        print("🔧 [COP浏览器] 开始应用初始化")
        
        // 初始化安全服务
        _ = SecurityService.shared
        
        // 初始化浏览器管理器
        _ = BrowserManager.shared
        
        // 配置网络优化
        await configureNetworkOptimizations()
        
        print("✅ [COP浏览器] 应用初始化完成")
    }
    
    // MARK: - 网络优化配置
    private func configureNetworkOptimizations() async {
        // 应用网络安全配置
        SecurityService.shared.applyNetworkSecurityConfiguration()
        
        // 预热DNS解析
        Task.detached {
            do {
                _ = try await URLSession.shared.data(from: URL(string: "https://www.google.com")!)
            } catch {
                // 网络预热失败是正常的
                await SystemErrorHandler.shared.handleNetworkError(error, context: "DNS预热")
            }
        }
    }
    
    // MARK: - 应用终止处理
    @MainActor
    private func handleAppTermination() async {
        print("📱 [COP浏览器] 应用即将终止")
        
        // 生成最终诊断报告
        let report = SystemErrorHandler.shared.generateDiagnosticReport()
        print(report)
        
        // 保存数据
        browserViewModel.saveTabs()
    }
    
    // MARK: - 应用后台处理
    @MainActor
    private func handleAppBackground() async {
        print("📱 [COP浏览器] 应用进入后台")
        
        // 保存数据
        browserViewModel.saveTabs()
        
        // 优化内存使用
        await BrowserManager.shared.optimizeMemory()
    }
}
